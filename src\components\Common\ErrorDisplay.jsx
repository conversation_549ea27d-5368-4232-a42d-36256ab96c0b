import { AlertTriangle, RefreshC<PERSON>, Home, AlertCircle, XCircle } from 'lucide-react'
import { Link } from 'react-router-dom'

const ErrorDisplay = ({ 
  error, 
  title = 'Something went wrong',
  message = null,
  showRetry = true,
  onRetry = null,
  showHome = false,
  variant = 'default', // 'default', 'inline', 'minimal'
  className = ''
}) => {
  // Extract error message
  const errorMessage = message || 
    error?.response?.data?.message || 
    error?.message || 
    'An unexpected error occurred. Please try again.'

  // Get appropriate icon based on error type
  const getIcon = () => {
    if (error?.response?.status >= 500) {
      return XCircle
    }
    if (error?.response?.status >= 400) {
      return AlertCircle
    }
    return AlertTriangle
  }

  const Icon = getIcon()

  // Minimal variant for inline errors
  if (variant === 'minimal') {
    return (
      <div className={`flex items-center space-x-2 text-error-600 ${className}`}>
        <Icon className="w-4 h-4 flex-shrink-0" />
        <span className="text-sm">{errorMessage}</span>
        {showRetry && onRetry && (
          <button
            onClick={onRetry}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium ml-2"
          >
            Retry
          </button>
        )}
      </div>
    )
  }

  // Inline variant for smaller spaces
  if (variant === 'inline') {
    return (
      <div className={`bg-error-50 border border-error-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-start space-x-3">
          <Icon className="w-5 h-5 text-error-600 flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-error-800 mb-1">
              {title}
            </h3>
            <p className="text-sm text-error-700">
              {errorMessage}
            </p>
            {(showRetry || showHome) && (
              <div className="mt-3 flex space-x-3">
                {showRetry && onRetry && (
                  <button
                    onClick={onRetry}
                    className="btn btn-error btn-sm"
                  >
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Retry
                  </button>
                )}
                {showHome && (
                  <Link
                    to="/dashboard"
                    className="btn btn-secondary btn-sm"
                  >
                    <Home className="w-3 h-3 mr-1" />
                    Dashboard
                  </Link>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Default variant for full page errors
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="w-16 h-16 bg-error-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <Icon className="w-8 h-8 text-error-600" />
      </div>

      <h2 className="text-2xl font-bold text-gray-900 mb-4">
        {title}
      </h2>
      
      <p className="text-gray-600 mb-8 max-w-md mx-auto">
        {errorMessage}
      </p>

      {/* Error Details (Development only) */}
      {process.env.NODE_ENV === 'development' && error && (
        <details className="bg-gray-100 rounded-lg p-4 mb-6 text-left max-w-2xl mx-auto">
          <summary className="cursor-pointer text-sm font-medium text-gray-900 mb-2">
            Error Details (Development)
          </summary>
          <pre className="text-xs text-gray-700 overflow-auto whitespace-pre-wrap">
            {JSON.stringify(error, null, 2)}
          </pre>
        </details>
      )}

      {/* Action Buttons */}
      {(showRetry || showHome) && (
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {showRetry && onRetry && (
            <button
              onClick={onRetry}
              className="btn btn-primary"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </button>
          )}
          
          {showHome && (
            <Link
              to="/dashboard"
              className="btn btn-secondary"
            >
              <Home className="w-4 h-4 mr-2" />
              Go to Dashboard
            </Link>
          )}
        </div>
      )}
    </div>
  )
}

// Specific error components for common scenarios
export const NetworkError = ({ onRetry }) => (
  <ErrorDisplay
    title="Connection Problem"
    message="Unable to connect to the server. Please check your internet connection and try again."
    onRetry={onRetry}
    showHome={true}
  />
)

export const NotFoundError = ({ resource = 'page' }) => (
  <ErrorDisplay
    title={`${resource.charAt(0).toUpperCase() + resource.slice(1)} Not Found`}
    message={`The ${resource} you're looking for doesn't exist or has been removed.`}
    showRetry={false}
    showHome={true}
  />
)

export const UnauthorizedError = () => (
  <ErrorDisplay
    title="Access Denied"
    message="You don't have permission to access this resource. Please log in or contact an administrator."
    showRetry={false}
    showHome={true}
  />
)

export const ServerError = ({ onRetry }) => (
  <ErrorDisplay
    title="Server Error"
    message="Our servers are experiencing issues. Please try again in a few moments."
    onRetry={onRetry}
    showHome={true}
  />
)

export default ErrorDisplay
