import { useState } from 'react'
import { Link } from 'react-router-dom'
import {
  Users,
  FileText,
  MessageSquare,
  TrendingUp,
  Plus,
  Calendar,
  Activity,
  Eye,
  Heart
} from 'lucide-react'
import { useAuth } from '../context/AuthContext'
import { usePosts, usePostsWithCommentCount } from '../hooks/useApi'
import { formatRelativeTime, truncateText } from '../utils/helpers'
import StatsCard from '../components/Dashboard/StatsCard'
import ActivityFeed from '../components/Dashboard/ActivityFeed'
import QuickActions from '../components/Dashboard/QuickActions'

const DashboardPage = () => {
  const { user } = useAuth()
  const { data: postsData, isLoading: postsLoading } = usePosts()
  const { data: postsWithCountData, isLoading: countLoading } = usePostsWithCommentCount()

  // Calculate stats
  const totalPosts = postsData?.data?.count || 0
  const totalComments = postsWithCountData?.data?.posts?.reduce((sum, post) =>
    sum + parseInt(post.commentCount || 0), 0) || 0
  const userPosts = postsData?.data?.posts?.filter(post => post.userId === user?.id) || []

  const stats = [
    {
      title: 'Total Posts',
      value: totalPosts,
      icon: FileText,
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'positive'
    },
    {
      title: 'Total Comments',
      value: totalComments,
      icon: MessageSquare,
      color: 'bg-green-500',
      change: '+8%',
      changeType: 'positive'
    },
    {
      title: 'My Posts',
      value: userPosts.length,
      icon: Users,
      color: 'bg-purple-500',
      change: '+5%',
      changeType: 'positive'
    },
    {
      title: 'Engagement',
      value: '94%',
      icon: TrendingUp,
      color: 'bg-orange-500',
      change: '+2%',
      changeType: 'positive'
    }
  ]

  const recentPosts = postsData?.data?.posts?.slice(0, 5) || []

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">
              Welcome back, {user?.name}! 👋
            </h1>
            <p className="text-primary-100">
              Here's what's happening with your community today.
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <Link
              to="/posts/create"
              className="btn bg-white text-primary-600 hover:bg-gray-50"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Post
            </Link>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        {stats.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            change={stat.change}
            changeType={stat.changeType}
            loading={postsLoading || countLoading}
          />
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6 lg:gap-8">
        {/* Recent Posts */}
        <div className="xl:col-span-2">
          <RecentPostsCard
            posts={recentPosts}
            loading={postsLoading}
          />
        </div>

        {/* Quick Actions */}
        <div className="xl:col-span-1">
          <QuickActions />
        </div>

        {/* Activity Feed */}
        <div className="xl:col-span-1">
          <div className="card">
            <div className="card-body">
              <ActivityFeed
                loading={postsLoading}
                onRefresh={() => {
                  // Implement refresh logic if needed
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Recent Posts Card Component
const RecentPostsCard = ({ posts, loading }) => {
  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-gray-900">Recent Posts</h2>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">
            Recent Posts
          </h2>
          <Link
            to="/posts"
            className="text-primary-600 hover:text-primary-500 text-sm font-medium"
          >
            View all
          </Link>
        </div>
      </div>
      <div className="card-body">
        {posts.length > 0 ? (
          <div className="space-y-4">
            {posts.map((post) => (
              <div key={post.id} className="border-b border-gray-200 last:border-b-0 pb-4 last:pb-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <Link
                      to={`/posts/${post.id}`}
                      className="text-gray-900 hover:text-primary-600 font-medium block"
                    >
                      {post.title}
                    </Link>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {truncateText(post.content, 100)}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span className="flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatRelativeTime(post.createdAt)}
                      </span>
                      <span className="flex items-center">
                        <Users className="w-3 h-3 mr-1" />
                        {post.user?.name || 'Unknown'}
                      </span>
                      <span className="flex items-center">
                        <Eye className="w-3 h-3 mr-1" />
                        View
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No posts yet
            </h3>
            <p className="text-gray-600 mb-4">
              Start sharing your thoughts with the community!
            </p>
            <Link
              to="/posts/create"
              className="btn btn-primary"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create your first post
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}

export default DashboardPage
