import { Link, useLocation } from 'react-router-dom'
import { Home, FileText, MessageSquare, User, Plus } from 'lucide-react'

const MobileNav = () => {
  const location = useLocation()

  const navItems = [
    { name: 'Home', href: '/dashboard', icon: Home },
    { name: 'Posts', href: '/posts', icon: FileText },
    { name: 'Create', href: '/posts/create', icon: Plus },
    { name: 'Comments', href: '/comments', icon: MessageSquare },
    { name: 'Profile', href: '/profile', icon: User },
  ]

  const isActive = (href) => {
    return location.pathname === href
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 lg:hidden">
      <div className="grid grid-cols-5 h-16">
        {navItems.map((item) => (
          <Link
            key={item.name}
            to={item.href}
            className={`flex flex-col items-center justify-center space-y-1 transition-colors ${
              isActive(item.href)
                ? 'text-primary-600 bg-primary-50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <item.icon className="w-5 h-5" />
            <span className="text-xs font-medium">{item.name}</span>
          </Link>
        ))}
      </div>
    </div>
  )
}

export default MobileNav
