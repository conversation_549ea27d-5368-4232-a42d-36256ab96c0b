import { useState } from 'react'
import { MessageSquare, Search, Plus, TrendingUp } from 'lucide-react'
import CommentSearch from '../components/Comments/CommentSearch'
import BulkCommentCreator from '../components/Comments/BulkCommentCreator'
import NewestComments from '../components/Comments/NewestComments'
import { usePosts } from '../hooks/useApi'

const CommentsPage = () => {
  const [activeTab, setActiveTab] = useState('search') // 'search', 'create', 'recent'
  const { data: postsData } = usePosts()
  
  const posts = postsData?.data?.posts || []
  const recentPosts = posts.slice(0, 5)

  const tabs = [
    {
      id: 'search',
      name: 'Search Comments',
      icon: Search,
      description: 'Find comments across all posts'
    },
    {
      id: 'create',
      name: 'Bulk Create',
      icon: Plus,
      description: 'Create multiple comments at once'
    },
    {
      id: 'recent',
      name: 'Recent Activity',
      icon: TrendingUp,
      description: 'View latest comments from recent posts'
    }
  ]

  const handleBulkCreateSuccess = () => {
    setActiveTab('recent')
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'search':
        return <CommentSearch />
      
      case 'create':
        return (
          <BulkCommentCreator
            onSuccess={handleBulkCreateSuccess}
            onCancel={() => setActiveTab('search')}
          />
        )
      
      case 'recent':
        return (
          <div className="space-y-8">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Recent Comment Activity
              </h1>
              <p className="text-gray-600 max-w-2xl mx-auto">
                View the latest comments from recent posts in the community.
              </p>
            </div>

            {recentPosts.length === 0 ? (
              <div className="text-center py-12">
                <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  No posts available
                </h2>
                <p className="text-gray-600">
                  Create some posts first to see comment activity.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {recentPosts.map((post) => (
                  <div key={post.id} className="space-y-4">
                    <div className="card">
                      <div className="card-header">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {post.title}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">
                          by {post.user?.name || 'Unknown User'}
                        </p>
                      </div>
                      <div className="card-body">
                        <NewestComments 
                          postId={post.id} 
                          limit={3}
                          showPostTitle={false}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )
      
      default:
        return null
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <MessageSquare className="w-8 h-8 text-primary-600" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Comment Management
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Manage, search, and create comments across all posts in the community.
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="card">
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`p-4 rounded-lg border-2 transition-all text-left ${
                  activeTab === tab.id
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-3 mb-2">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                    activeTab === tab.id
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    <tab.icon className="w-4 h-4" />
                  </div>
                  <h3 className={`font-semibold ${
                    activeTab === tab.id ? 'text-primary-900' : 'text-gray-900'
                  }`}>
                    {tab.name}
                  </h3>
                </div>
                <p className={`text-sm ${
                  activeTab === tab.id ? 'text-primary-700' : 'text-gray-600'
                }`}>
                  {tab.description}
                </p>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {renderTabContent()}
      </div>
    </div>
  )
}

export default CommentsPage
