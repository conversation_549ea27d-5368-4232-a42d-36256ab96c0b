import { useState } from 'react'
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import {
  ArrowLeft,
  Calendar,
  User,
  MessageSquare,
  Send,
  Edit,
  Trash2,
  MoreHorizontal,
  Heart,
  Share
} from 'lucide-react'
import { usePost, useCreateComment, useDeletePost } from '../hooks/useApi'
import { useAuth } from '../context/AuthContext'
import { formatRelativeTime, formatDateTime } from '../utils/helpers'
import CommentEditor from '../components/Comments/CommentEditor'

const PostDetailPage = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { user } = useAuth()
  const [showCommentForm, setShowCommentForm] = useState(false)

  const { data: postData, isLoading, error } = usePost(id)
  const createCommentMutation = useCreateComment()
  const deletePostMutation = useDeletePost()

  const post = postData?.data?.post

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm()

  const canEditPost = () => {
    return user && post && (user.id === post.userId || user.role === 'admin')
  }

  const handleDeletePost = async () => {
    if (window.confirm('Are you sure you want to delete this post?')) {
      try {
        await deletePostMutation.mutateAsync(id)
        navigate('/posts')
      } catch (error) {
        console.error('Failed to delete post:', error)
      }
    }
  }

  const onSubmitComment = async (data) => {
    try {
      await createCommentMutation.mutateAsync({
        content: data.content,
        postId: parseInt(id)
      })
      reset()
      setShowCommentForm(false)
    } catch (error) {
      console.error('Failed to create comment:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="card">
            <div className="card-body">
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="space-y-2 mb-6">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                <div className="h-4 bg-gray-200 rounded w-4/6"></div>
              </div>
              <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !post) {
    return (
      <div className="max-w-4xl mx-auto text-center py-20">
        <div className="text-error-600 mb-4">
          <MessageSquare className="w-16 h-16 mx-auto mb-4" />
          <h2 className="text-2xl font-bold">Post not found</h2>
          <p className="text-gray-600 mt-2">
            The post you're looking for doesn't exist or has been deleted.
          </p>
          <Link
            to="/posts"
            className="btn btn-primary mt-6"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Posts
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Link
          to="/posts"
          className="btn btn-ghost btn-sm"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Posts
        </Link>

        {canEditPost() && (
          <div className="flex items-center space-x-2">
            <Link
              to={`/posts/${post.id}/edit`}
              className="btn btn-secondary btn-sm"
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Link>
            <button
              onClick={handleDeletePost}
              disabled={deletePostMutation.isLoading}
              className="btn btn-error btn-sm"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </button>
          </div>
        )}
      </div>

      {/* Post Content */}
      <article className="card">
        <div className="card-body">
          {/* Post Header */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-primary-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">
                  {post.user?.name || 'Unknown User'}
                </h3>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <span className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {formatDateTime(post.createdAt)}
                  </span>
                  {post.updatedAt !== post.createdAt && (
                    <span className="text-xs">
                      (edited {formatRelativeTime(post.updatedAt)})
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Post Title */}
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            {post.title}
          </h1>

          {/* Post Content */}
          <div className="prose max-w-none mb-8">
            <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
              {post.content}
            </div>
          </div>

          {/* Post Actions */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div className="flex items-center space-x-6">
              <button className="flex items-center space-x-2 text-gray-500 hover:text-primary-600 transition-colors">
                <Heart className="w-5 h-5" />
                <span>Like</span>
              </button>
              <button
                onClick={() => setShowCommentForm(!showCommentForm)}
                className="flex items-center space-x-2 text-gray-500 hover:text-primary-600 transition-colors"
              >
                <MessageSquare className="w-5 h-5" />
                <span>Comment</span>
              </button>
              <button className="flex items-center space-x-2 text-gray-500 hover:text-primary-600 transition-colors">
                <Share className="w-5 h-5" />
                <span>Share</span>
              </button>
            </div>
            <span className="text-sm text-gray-500">
              {post.comments?.length || 0} comment{(post.comments?.length || 0) !== 1 ? 's' : ''}
            </span>
          </div>
        </div>
      </article>

      {/* Comment Form */}
      {showCommentForm && user && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Add a Comment</h3>
          </div>
          <div className="card-body">
            <form onSubmit={handleSubmit(onSubmitComment)} className="space-y-4">
              <div>
                <textarea
                  rows={4}
                  className={`input resize-none ${errors.content ? 'input-error' : ''}`}
                  placeholder="Share your thoughts..."
                  {...register('content', {
                    required: 'Comment content is required',
                    minLength: {
                      value: 1,
                      message: 'Comment must be at least 1 character'
                    },
                    maxLength: {
                      value: 1000,
                      message: 'Comment must be less than 1000 characters'
                    }
                  })}
                />
                {errors.content && (
                  <p className="mt-1 text-sm text-error-600">
                    {errors.content.message}
                  </p>
                )}
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowCommentForm(false)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createCommentMutation.isLoading}
                  className="btn btn-primary"
                >
                  {createCommentMutation.isLoading ? (
                    <div className="flex items-center">
                      <div className="spinner w-4 h-4 mr-2"></div>
                      Posting...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <Send className="w-4 h-4 mr-2" />
                      Post Comment
                    </div>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Login prompt for non-authenticated users */}
      {showCommentForm && !user && (
        <div className="card">
          <div className="card-body text-center">
            <p className="text-gray-600 mb-4">
              You need to be logged in to comment on this post.
            </p>
            <div className="space-x-3">
              <Link to="/login" className="btn btn-primary">
                Sign In
              </Link>
              <Link to="/signup" className="btn btn-secondary">
                Sign Up
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Comments Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">
            Comments ({post.comments?.length || 0})
          </h2>
          {!showCommentForm && user && (
            <button
              onClick={() => setShowCommentForm(true)}
              className="btn btn-primary btn-sm"
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              Add Comment
            </button>
          )}
        </div>

        {post.comments && post.comments.length > 0 ? (
          <div className="space-y-4">
            {post.comments.map((comment) => (
              <CommentCard key={comment.id} comment={comment} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No comments yet
            </h3>
            <p className="text-gray-600 mb-4">
              Be the first to share your thoughts on this post!
            </p>
            {user && !showCommentForm && (
              <button
                onClick={() => setShowCommentForm(true)}
                className="btn btn-primary"
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Add First Comment
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// Comment Card Component
const CommentCard = ({ comment }) => {
  return (
    <div className="card">
      <div className="card-body">
        <div className="flex items-start space-x-3">
          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
            <User className="w-5 h-5 text-primary-600" />
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h4 className="font-medium text-gray-900">
                {comment.user?.name || 'Unknown User'}
              </h4>
              <span className="text-sm text-gray-500">
                {formatRelativeTime(comment.createdAt)}
              </span>
            </div>
            <CommentEditor
              comment={comment}
              onSuccess={() => {
                // Optionally refetch post data or show success message
              }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default PostDetailPage
