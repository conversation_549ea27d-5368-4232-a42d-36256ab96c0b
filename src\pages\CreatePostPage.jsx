import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { ArrowLeft, FileText, Save, Eye } from 'lucide-react'
import { useCreatePost } from '../hooks/useApi'
import { useAuth } from '../context/AuthContext'

const CreatePostPage = () => {
  const [isPreview, setIsPreview] = useState(false)
  const { user } = useAuth()
  const navigate = useNavigate()
  const createPostMutation = useCreatePost()

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setError
  } = useForm()

  const watchedTitle = watch('title', '')
  const watchedContent = watch('content', '')

  const onSubmit = async (data) => {
    try {
      await createPostMutation.mutateAsync(data)
      navigate('/posts')
    } catch (error) {
      setError('root', {
        type: 'manual',
        message: error.response?.data?.message || 'Failed to create post'
      })
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/posts')}
            className="btn btn-ghost btn-sm"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Posts
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create New Post</h1>
            <p className="text-gray-600">Share your thoughts with the community</p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={() => setIsPreview(!isPreview)}
            className={`btn btn-sm ${isPreview ? 'btn-primary' : 'btn-secondary'}`}
          >
            <Eye className="w-4 h-4 mr-2" />
            {isPreview ? 'Edit' : 'Preview'}
          </button>
        </div>
      </div>

      {/* Form */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Editor */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              {isPreview ? 'Preview' : 'Write Post'}
            </h2>
          </div>
          <div className="card-body">
            {!isPreview ? (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Title */}
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                    Post Title
                  </label>
                  <input
                    id="title"
                    type="text"
                    className={`input ${errors.title ? 'input-error' : ''}`}
                    placeholder="Enter an engaging title..."
                    {...register('title', {
                      required: 'Title is required',
                      minLength: {
                        value: 1,
                        message: 'Title must be at least 1 character'
                      },
                      maxLength: {
                        value: 255,
                        message: 'Title must be less than 255 characters'
                      }
                    })}
                  />
                  {errors.title && (
                    <p className="mt-1 text-sm text-error-600">
                      {errors.title.message}
                    </p>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    {watchedTitle.length}/255 characters
                  </p>
                </div>

                {/* Content */}
                <div>
                  <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                    Post Content
                  </label>
                  <textarea
                    id="content"
                    rows={12}
                    className={`input resize-none ${errors.content ? 'input-error' : ''}`}
                    placeholder="Write your post content here..."
                    {...register('content', {
                      required: 'Content is required',
                      minLength: {
                        value: 1,
                        message: 'Content must be at least 1 character'
                      },
                      maxLength: {
                        value: 10000,
                        message: 'Content must be less than 10000 characters'
                      }
                    })}
                  />
                  {errors.content && (
                    <p className="mt-1 text-sm text-error-600">
                      {errors.content.message}
                    </p>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    {watchedContent.length}/10000 characters
                  </p>
                </div>

                {/* Error Message */}
                {errors.root && (
                  <div className="bg-error-50 border border-error-200 rounded-lg p-3">
                    <p className="text-sm text-error-600">
                      {errors.root.message}
                    </p>
                  </div>
                )}

                {/* Submit Button */}
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => navigate('/posts')}
                    className="btn btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={createPostMutation.isLoading}
                    className="btn btn-primary"
                  >
                    {createPostMutation.isLoading ? (
                      <div className="flex items-center">
                        <div className="spinner w-4 h-4 mr-2"></div>
                        Publishing...
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <Save className="w-4 h-4 mr-2" />
                        Publish Post
                      </div>
                    )}
                  </button>
                </div>
              </form>
            ) : (
              /* Preview */
              <div className="space-y-6">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    {watchedTitle || 'Untitled Post'}
                  </h3>
                  <div className="prose max-w-none">
                    <div className="whitespace-pre-wrap text-gray-700">
                      {watchedContent || 'No content yet...'}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Author Info */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">Author</h3>
            </div>
            <div className="card-body">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-primary-600 font-medium">
                    {user?.name?.charAt(0)?.toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">{user?.name}</p>
                  <p className="text-sm text-gray-500">{user?.email}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Writing Tips */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">Writing Tips</h3>
            </div>
            <div className="card-body">
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Write a clear, descriptive title</li>
                <li>• Use proper formatting and paragraphs</li>
                <li>• Keep your content engaging and relevant</li>
                <li>• Preview your post before publishing</li>
                <li>• Be respectful and follow community guidelines</li>
              </ul>
            </div>
          </div>

          {/* Post Stats */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">Post Stats</h3>
            </div>
            <div className="card-body">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Title length:</span>
                  <span className="text-sm font-medium">{watchedTitle.length} chars</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Content length:</span>
                  <span className="text-sm font-medium">{watchedContent.length} chars</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Estimated read time:</span>
                  <span className="text-sm font-medium">
                    {Math.max(1, Math.ceil(watchedContent.split(' ').length / 200))} min
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreatePostPage
