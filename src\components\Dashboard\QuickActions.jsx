import { Link } from 'react-router-dom'
import { 
  Plus, 
  FileText, 
  MessageSquare, 
  Users, 
  Search,
  Settings,
  TrendingUp,
  BarChart3
} from 'lucide-react'
import { useAuth } from '../../context/AuthContext'

const QuickActions = () => {
  const { user } = useAuth()

  const actions = [
    {
      title: 'Create New Post',
      description: 'Share your thoughts with the community',
      icon: Plus,
      href: '/posts/create',
      color: 'bg-primary-600 hover:bg-primary-700',
      featured: true
    },
    {
      title: 'Browse Posts',
      description: 'Discover content from other users',
      icon: FileText,
      href: '/posts',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      title: 'Search Comments',
      description: 'Find specific discussions',
      icon: Search,
      href: '/comments',
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      title: 'View Profile',
      description: 'Manage your account settings',
      icon: Users,
      href: '/profile',
      color: 'bg-purple-600 hover:bg-purple-700'
    }
  ]

  // Add admin actions if user is admin
  if (user?.role === 'admin') {
    actions.push(
      {
        title: 'Manage Users',
        description: 'Admin panel for user management',
        icon: Users,
        href: '/admin/users',
        color: 'bg-orange-600 hover:bg-orange-700'
      },
      {
        title: 'Analytics',
        description: 'View platform statistics',
        icon: BarChart3,
        href: '/admin/analytics',
        color: 'bg-indigo-600 hover:bg-indigo-700'
      }
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Quick Actions
        </h3>
        <Link
          to="/settings"
          className="text-gray-400 hover:text-gray-600"
          title="Settings"
        >
          <Settings className="w-5 h-5" />
        </Link>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {actions.map((action, index) => (
          <ActionCard key={index} action={action} />
        ))}
      </div>

      {/* Featured Action */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white">
        <div className="flex items-center space-x-3 mb-3">
          <TrendingUp className="w-6 h-6" />
          <h4 className="text-lg font-semibold">Get Started</h4>
        </div>
        <p className="text-primary-100 mb-4">
          Welcome to PostHub! Start by creating your first post or exploring content from other users.
        </p>
        <div className="flex space-x-3">
          <Link
            to="/posts/create"
            className="btn bg-white text-primary-600 hover:bg-gray-50 btn-sm"
          >
            Create Post
          </Link>
          <Link
            to="/posts"
            className="btn border-2 border-white text-white hover:bg-white hover:text-primary-600 btn-sm"
          >
            Browse Posts
          </Link>
        </div>
      </div>
    </div>
  )
}

// Action Card Component
const ActionCard = ({ action }) => {
  const { title, description, icon: Icon, href, color, featured } = action

  return (
    <Link
      to={href}
      className={`block p-4 rounded-lg border-2 transition-all hover:shadow-medium ${
        featured
          ? 'border-primary-200 bg-primary-50 hover:border-primary-300'
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
      }`}
    >
      <div className="flex items-start space-x-3">
        <div className={`w-10 h-10 ${color} rounded-lg flex items-center justify-center flex-shrink-0`}>
          <Icon className="w-5 h-5 text-white" />
        </div>
        <div className="flex-1 min-w-0">
          <h4 className={`font-medium ${featured ? 'text-primary-900' : 'text-gray-900'}`}>
            {title}
          </h4>
          <p className={`text-sm mt-1 ${featured ? 'text-primary-700' : 'text-gray-600'}`}>
            {description}
          </p>
        </div>
      </div>
    </Link>
  )
}

export default QuickActions
