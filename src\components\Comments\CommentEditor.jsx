import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Edit, Save, X, MessageSquare } from 'lucide-react'
import { useUpdateComment } from '../../hooks/useApi'
import { useAuth } from '../../context/AuthContext'

const CommentEditor = ({ comment, onCancel, onSuccess }) => {
  const [isEditing, setIsEditing] = useState(false)
  const { user } = useAuth()
  const updateCommentMutation = useUpdateComment()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setError
  } = useForm({
    defaultValues: {
      content: comment.content
    }
  })

  const canEdit = user && (user.id === comment.userId || user.role === 'admin')

  const onSubmit = async (data) => {
    try {
      await updateCommentMutation.mutateAsync({
        commentId: comment.id,
        commentData: {
          content: data.content,
          userId: comment.userId
        }
      })
      setIsEditing(false)
      onSuccess?.()
    } catch (error) {
      setError('root', {
        type: 'manual',
        message: error.response?.data?.message || 'Failed to update comment'
      })
    }
  }

  const handleCancel = () => {
    reset()
    setIsEditing(false)
    onCancel?.()
  }

  if (!canEdit) {
    return (
      <div className="text-gray-700 whitespace-pre-wrap">
        {comment.content}
      </div>
    )
  }

  if (!isEditing) {
    return (
      <div className="group">
        <div className="text-gray-700 whitespace-pre-wrap mb-2">
          {comment.content}
        </div>
        <button
          onClick={() => setIsEditing(true)}
          className="opacity-0 group-hover:opacity-100 transition-opacity text-primary-600 hover:text-primary-700 text-sm flex items-center"
        >
          <Edit className="w-3 h-3 mr-1" />
          Edit
        </button>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-3">
      <div>
        <textarea
          rows={4}
          className={`input resize-none w-full ${errors.content ? 'input-error' : ''}`}
          placeholder="Edit your comment..."
          {...register('content', {
            required: 'Comment content is required',
            minLength: {
              value: 1,
              message: 'Comment must be at least 1 character'
            },
            maxLength: {
              value: 1000,
              message: 'Comment must be less than 1000 characters'
            }
          })}
        />
        {errors.content && (
          <p className="mt-1 text-sm text-error-600">
            {errors.content.message}
          </p>
        )}
      </div>

      {/* Error Message */}
      {errors.root && (
        <div className="bg-error-50 border border-error-200 rounded-lg p-2">
          <p className="text-sm text-error-600">
            {errors.root.message}
          </p>
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-end space-x-2">
        <button
          type="button"
          onClick={handleCancel}
          className="btn btn-secondary btn-sm"
        >
          <X className="w-3 h-3 mr-1" />
          Cancel
        </button>
        <button
          type="submit"
          disabled={updateCommentMutation.isLoading}
          className="btn btn-primary btn-sm"
        >
          {updateCommentMutation.isLoading ? (
            <div className="flex items-center">
              <div className="spinner w-3 h-3 mr-1"></div>
              Saving...
            </div>
          ) : (
            <div className="flex items-center">
              <Save className="w-3 h-3 mr-1" />
              Save
            </div>
          )}
        </button>
      </div>
    </form>
  )
}

export default CommentEditor
