import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import {
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Calendar,
  User,
  MessageSquare,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal
} from 'lucide-react'
import { usePosts, useDeletePost } from '../hooks/useApi'
import { useAuth } from '../context/AuthContext'
import { formatRelativeTime, truncateText } from '../utils/helpers'

const PostsPage = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState('grid') // 'grid' or 'list'
  const [sortBy, setSortBy] = useState('newest') // 'newest', 'oldest', 'title'

  const { user } = useAuth()
  const { data: postsData, isLoading, error } = usePosts()
  const deletePostMutation = useDeletePost()

  const posts = postsData?.data?.posts || []

  // Filter and sort posts
  const filteredPosts = posts
    .filter(post =>
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.content.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'oldest':
          return new Date(a.createdAt) - new Date(b.createdAt)
        case 'title':
          return a.title.localeCompare(b.title)
        case 'newest':
        default:
          return new Date(b.createdAt) - new Date(a.createdAt)
      }
    })

  const handleDeletePost = async (postId) => {
    if (window.confirm('Are you sure you want to delete this post?')) {
      try {
        await deletePostMutation.mutateAsync(postId)
      } catch (error) {
        console.error('Failed to delete post:', error)
      }
    }
  }

  const canEditPost = (post) => {
    return user?.id === post.userId || user?.role === 'admin'
  }

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="card animate-pulse">
              <div className="card-body">
                <div className="h-6 bg-gray-200 rounded mb-4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-20">
        <div className="text-error-600 mb-4">
          <MessageSquare className="w-16 h-16 mx-auto mb-4" />
          <h2 className="text-2xl font-bold">Failed to load posts</h2>
          <p className="text-gray-600 mt-2">Please try again later</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Posts</h1>
          <p className="text-gray-600 mt-1">
            Discover and share amazing content with the community
          </p>
        </div>
        <Link
          to="/posts/create"
          className="btn btn-primary"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Post
        </Link>
      </div>

      {/* Filters and Search */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search posts..."
                  className="input pl-10 w-full"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Sort */}
            <div className="flex items-center space-x-4">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="input w-auto"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="title">Title A-Z</option>
              </select>

              {/* View Mode Toggle */}
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-primary-100 text-primary-600' : 'text-gray-400'}`}
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-primary-100 text-primary-600' : 'text-gray-400'}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Results count */}
          <div className="mt-4 text-sm text-gray-600">
            {filteredPosts.length} post{filteredPosts.length !== 1 ? 's' : ''} found
            {searchTerm && ` for "${searchTerm}"`}
          </div>
        </div>
      </div>

      {/* Posts Display */}
      {filteredPosts.length === 0 ? (
        <div className="text-center py-20">
          <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {searchTerm ? 'No posts found' : 'No posts yet'}
          </h2>
          <p className="text-gray-600 mb-6">
            {searchTerm
              ? 'Try adjusting your search terms'
              : 'Be the first to share something with the community!'
            }
          </p>
          {!searchTerm && (
            <Link
              to="/posts/create"
              className="btn btn-primary"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create First Post
            </Link>
          )}
        </div>
      ) : (
        <div className={viewMode === 'grid'
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
          : 'space-y-4'
        }>
          {filteredPosts.map((post) => (
            <PostCard
              key={post.id}
              post={post}
              viewMode={viewMode}
              canEdit={canEditPost(post)}
              onDelete={() => handleDeletePost(post.id)}
              isDeleting={deletePostMutation.isLoading}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Post Card Component
const PostCard = ({ post, viewMode, canEdit, onDelete, isDeleting }) => {
  const [showMenu, setShowMenu] = useState(false)

  if (viewMode === 'list') {
    return (
      <div className="card hover:shadow-medium transition-shadow">
        <div className="card-body">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-primary-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {post.user?.name || 'Unknown User'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatRelativeTime(post.createdAt)}
                  </p>
                </div>
              </div>

              <Link
                to={`/posts/${post.id}`}
                className="block group"
              >
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors mb-2">
                  {post.title}
                </h3>
                <p className="text-gray-600 line-clamp-2">
                  {truncateText(post.content, 150)}
                </p>
              </Link>

              <div className="flex items-center space-x-4 mt-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <Eye className="w-4 h-4 mr-1" />
                  View
                </span>
                <span className="flex items-center">
                  <MessageSquare className="w-4 h-4 mr-1" />
                  Comments
                </span>
              </div>
            </div>

            {canEdit && (
              <div className="relative">
                <button
                  onClick={() => setShowMenu(!showMenu)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                >
                  <MoreHorizontal className="w-4 h-4" />
                </button>

                {showMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                    <div className="py-1">
                      <Link
                        to={`/posts/${post.id}/edit`}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Post
                      </Link>
                      <button
                        onClick={onDelete}
                        disabled={isDeleting}
                        className="flex items-center w-full px-4 py-2 text-sm text-error-600 hover:bg-error-50"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete Post
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Grid view
  return (
    <div className="card hover:shadow-medium transition-shadow">
      <div className="card-body">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-primary-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">
                {post.user?.name || 'Unknown User'}
              </p>
              <p className="text-xs text-gray-500">
                {formatRelativeTime(post.createdAt)}
              </p>
            </div>
          </div>

          {canEdit && (
            <div className="relative">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-100"
              >
                <MoreHorizontal className="w-4 h-4" />
              </button>

              {showMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    <Link
                      to={`/posts/${post.id}/edit`}
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Post
                    </Link>
                    <button
                      onClick={onDelete}
                      disabled={isDeleting}
                      className="flex items-center w-full px-4 py-2 text-sm text-error-600 hover:bg-error-50"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Post
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <Link
          to={`/posts/${post.id}`}
          className="block group"
        >
          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors mb-2">
            {post.title}
          </h3>
          <p className="text-gray-600 line-clamp-3">
            {truncateText(post.content, 120)}
          </p>
        </Link>

        <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span className="flex items-center">
              <Eye className="w-4 h-4 mr-1" />
              View
            </span>
            <span className="flex items-center">
              <MessageSquare className="w-4 h-4 mr-1" />
              Comments
            </span>
          </div>
          <span className="text-xs text-gray-400">
            <Calendar className="w-3 h-3 inline mr-1" />
            {formatRelativeTime(post.createdAt)}
          </span>
        </div>
      </div>
    </div>
  )
}

export default PostsPage
